import {
  ConflictException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { UserRole, InvitationStatus, UserStatus } from 'generated/prisma';

import { PrismaService } from 'src/prisma.service';
import { InvitationTokenPayload } from 'src/token/interfaces/payload.interface';
import { TokenService } from 'src/token/token.service';
import { CreateUserDto } from 'src/users/dto/create-user.dto';
import { CreateInvitationDto } from './dto/create-invitation.dto';
import { UsersService } from 'src/users/users.service';
import { MailerService } from 'src/mailer/mailer.service';
import { invitationTemplate } from 'src/mailer/templates/invitation.template';

@Injectable()
export class InvitationService {
  private readonly logger = new Logger(InvitationService.name);
  constructor(
    private readonly prismaService: PrismaService,
    private readonly tokenService: TokenService,
    private readonly usersService: UsersService,
    private readonly mailerService: MailerService,
  ) {}
  async createInvitation(invitation: CreateInvitationDto) {
    const existingInvitation = await this.prismaService.invitation.findUnique({
      where: { email: invitation.email },
    });

    if (existingInvitation) {
      throw new ConflictException('Invitation already exists for this email');
    }

    const existingUser = await this.prismaService.user.findUnique({
      where: { email: invitation.email },
    });

    if (existingUser) {
      throw new ConflictException('User already exists with this email');
    }

    const payload: InvitationTokenPayload = {
      sub: invitation.email,
      type: 'invitation',
    };

    const token = await this.tokenService.generateToken(payload, {
      expiresIn: '7d',
    });

    try {
      await this.prismaService.invitation.create({
        data: {
          email: invitation.email,
          status: InvitationStatus.PENDING,
          token: token.value,
        },
        select: {
          id: true,
          email: true,
          status: true,
          token: true,
        },
      });

      await this.sendInvitationEmail(invitation.email, token.value);
      return { message: 'Invitation sent', statusCode: HttpStatus.CREATED };
    } catch (error) {
      this.logger.error(error);
      throw new InternalServerErrorException('Invitation creation failed');
    }
  }

  async rejectInvitation(invitationId: string) {
    try {
      const invitation = await this.prismaService.invitation.findUnique({
        where: { id: invitationId },
      });

      if (!invitation) {
        throw new NotFoundException('Invitation not found');
      }

      await this.prismaService.invitation.update({
        where: { id: invitation.id },
        data: { status: InvitationStatus.REJECTED },
      });

      return {
        message: 'Invitation rejected successfully',
        statusCode: HttpStatus.OK,
      };
    } catch (error) {
      this.logger.error(error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to reject invitation');
    }
  }

  async listInvitations() {
    try {
      const invitationsList = await this.prismaService.invitation.findMany({
        where: { status: InvitationStatus.PENDING },
        select: {
          id: true,
          email: true,
          status: true,
          createdAt: true,
        },
      });
      return {
        message: 'Success',
        data: invitationsList,
        statusCode: HttpStatus.OK,
      };
    } catch (error) {
      this.logger.error(error);
      throw new InternalServerErrorException('Failed to list invitations');
    }
  }

  async getInvitationById(invitationId: string) {
    try {
      const invitation = await this.prismaService.invitation.findUnique({
        where: { id: invitationId },
      });

      if (!invitation) {
        throw new NotFoundException('Invitation not found');
      }

      return {
        message: 'Success',
        data: invitation,
        statusCode: HttpStatus.OK,
      };
    } catch (error) {
      this.logger.error(error);
      throw new InternalServerErrorException('Failed to get invitation');
    }
  }

  async getInvitationByToken(token: string) {
    try {
      const invitation = await this.prismaService.invitation.findUnique({
        where: { token: token },
      });
      if (!invitation) {
        throw new NotFoundException('Invitation not found');
      }
      return {
        message: 'Success',
        data: invitation,
        statusCode: HttpStatus.OK,
      };
    } catch (error) {
      this.logger.error(error);
      throw new InternalServerErrorException('Failed to get invitation');
    }
  }

  async acceptInvitation(token: string, userData: CreateUserDto) {
    try {
      const invitation = await this.getInvitationByToken(token);

      if (invitation.data.status !== InvitationStatus.PENDING) {
        throw new ConflictException('Invitation is not pending');
      }

      const userResponse = await this.usersService.createUser({
        ...userData,
        role: UserRole.USER,
        status: UserStatus.ACTIVE,
        email: invitation.data.email,
      });

      await this.prismaService.invitation.update({
        where: { id: invitation.data.id },
        data: {
          status: InvitationStatus.ACCEPTED,
          userId: userResponse.data.id,
        },
      });

      return {
        message: 'Invitation accepted successfully',
        data: userResponse.data,
        statusCode: HttpStatus.OK,
      };
    } catch (error) {
      this.logger.error(error);
      if (
        error instanceof ConflictException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to accept invitation');
    }
  }

  async sendInvitationEmail(email: string, token: string) {
    try {
      await this.mailerService.sendMail({
        to: email,
        subject: 'You are invited!',
        template: invitationTemplate({
          email,
          token,
        }),
      });

      return {
        message: 'Invitation email sent successfully',
        statusCode: HttpStatus.OK,
      };
    } catch (error) {
      this.logger.error(error);
      throw new InternalServerErrorException('Failed to send invitation email');
    }
  }

  async deleteInvitation(invitationId: string) {
    try {
      const invitation = await this.prismaService.invitation.findUnique({
        where: { id: invitationId },
      });

      if (!invitation) {
        throw new NotFoundException('Invitation not found');
      }

      await this.prismaService.invitation.delete({
        where: { id: invitationId },
      });

      return {
        message: 'Invitation deleted successfully',
        statusCode: HttpStatus.OK,
      };
    } catch (error) {
      this.logger.error(error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to delete invitation');
    }
  }
}
