import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  MessageEvent,
} from '@nestjs/common';
import { PrismaService } from '../prisma.service';
import { AssistantService } from '../assistant/assistant.service';
import {
  GenerationType,
  GenerationStatus,
  MessageRole,
  MessageStatus,
  UserRole,
} from 'generated/prisma';
import {
  AssistantRequest,
  AssistantMessage,
} from '../assistant/interfaces/assistant.interface';
import { Observable } from 'rxjs';

@Injectable()
export class GenerationService {
  private readonly logger = new Logger(GenerationService.name);

  constructor(
    private readonly prismaService: PrismaService,
    private readonly assistantService: AssistantService,
  ) {}

  async createConversationalGeneration(
    projectId: string,
    name: string,
    type: GenerationType,
    initialPrompt: string,
    createdById: string,
  ) {
    try {
      const project = await this.prismaService.project.findFirst({
        where: {
          id: projectId,
          OR: [{ createdById }, { members: { some: { userId: createdById } } }],
        },
      });

      if (!project) {
        throw new NotFoundException('Project not found or access denied');
      }

      const result = await this.prismaService.$transaction(async (tx) => {
        const generation = await tx.generation.create({
          data: {
            name,
            type,
            prompt: initialPrompt,
            initialPrompt,
            projectId,
            createdById,
            status: GenerationStatus.PENDING,
          },
        });

        await tx.conversationMessage.create({
          data: {
            generationId: generation.id,
            role: MessageRole.SYSTEM,
            content: `Starting ${type.toLowerCase()} generation: ${name}`,
            messageIndex: 0,
            status: MessageStatus.SENT,
          },
        });

        // Create initial user message
        await tx.conversationMessage.create({
          data: {
            generationId: generation.id,
            role: MessageRole.USER,
            content: initialPrompt,
            messageIndex: 1,
            createdById,
            status: MessageStatus.SENT,
          },
        });

        return generation;
      });

      this.logger.log(
        `Conversational generation created: ${result.id} for project ${projectId}`,
      );

      // Process initial AI response asynchronously
      void this.processInitialGeneration(
        result.id,
        type,
        initialPrompt,
        projectId,
      );

      return {
        message: 'Conversational generation created successfully',
        data: result,
        statusCode: 201,
      };
    } catch (error) {
      this.logger.error('Failed to create conversational generation', error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to create conversational generation',
      );
    }
  }

  async addMessageToConversation(
    generationId: string,
    content: string,
    createdById: string,
    inputData?: any,
  ) {
    try {
      // Validate generation exists and user has access
      const generation = await this.prismaService.generation.findFirst({
        where: {
          id: generationId,
          project: {
            OR: [
              { createdById },
              { members: { some: { userId: createdById } } },
            ],
          },
        },
        include: {
          project: true,
        },
      });

      if (!generation) {
        throw new NotFoundException('Generation not found or access denied');
      }

      // Get next message index
      const lastMessage =
        await this.prismaService.conversationMessage.findFirst({
          where: { generationId },
          orderBy: { messageIndex: 'desc' },
        });

      const nextIndex = (lastMessage?.messageIndex || 0) + 1;

      const userMessage = await this.prismaService.conversationMessage.create({
        data: {
          generationId,
          role: MessageRole.USER,
          content,
          messageIndex: nextIndex,
          createdById,
          inputData,
          status: MessageStatus.SENT,
        },
      });

      this.logger.log(
        `Message added to conversation ${generationId}: ${userMessage.id}`,
      );

      // Process AI response asynchronously
      void this.processConversationMessage(
        generationId,
        generation.type,
        generation.project.id,
      );

      return {
        message: 'Message added to conversation successfully',
        data: userMessage,
        statusCode: 201,
      };
    } catch (error) {
      this.logger.error('Failed to add message to conversation', error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Failed to add message to conversation');
    }
  }

  async getConversationHistory(
    generationId: string,
    userId: string,
    userRole: UserRole,
    page: number = 1,
    limit: number = 50,
  ) {
    try {
      const generation = await this.validateGenerationAccess(
        generationId,
        userId,
        userRole,
      );

      const skip = (page - 1) * limit;

      const [messages, totalCount] = await Promise.all([
        this.prismaService.conversationMessage.findMany({
          where: { generationId },
          include: {
            createdBy: {
              select: {
                id: true,
                email: true,
                fullName: true,
              },
            },
          },
          orderBy: { messageIndex: 'asc' },
          skip,
          take: limit,
        }),
        this.prismaService.conversationMessage.count({
          where: { generationId },
        }),
      ]);

      return {
        message: 'Conversation history retrieved successfully',
        data: {
          generation: {
            id: generation.id,
            name: generation.name,
            type: generation.type,
            status: generation.status,
            project: generation.project,
          },
          messages,
          pagination: {
            page,
            limit,
            total: totalCount,
            pages: Math.ceil(totalCount / limit),
          },
        },
        statusCode: 200,
      };
    } catch (error) {
      this.logger.error('Failed to get conversation history', error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Failed to get conversation history');
    }
  }

  async getCurrentResult(
    generationId: string,
    userId: string,
    userRole: UserRole,
  ) {
    try {
      const generation = await this.validateGenerationAccess(
        generationId,
        userId,
        userRole,
      );

      return {
        message: 'Current result retrieved successfully',
        data: {
          id: generation.id,
          name: generation.name,
          type: generation.type,
          status: generation.status,
          result: generation.result,
          currentResult: generation.currentResult,
          metadata: generation.metadata,
          project: generation.project,
          createdAt: generation.createdAt,
          updatedAt: generation.updatedAt,
        },
        statusCode: 200,
      };
    } catch (error) {
      this.logger.error('Failed to get current result', error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Failed to get current result');
    }
  }

  private async processInitialGeneration(
    generationId: string,
    type: GenerationType,
    initialPrompt: string,
    projectId: string,
  ) {
    try {
      // Update generation status to IN_PROGRESS
      await this.prismaService.generation.update({
        where: { id: generationId },
        data: { status: GenerationStatus.IN_PROGRESS },
      });

      // Get project context
      const project = await this.prismaService.project.findUnique({
        where: { id: projectId },
        select: { name: true, description: true },
      });

      // Prepare assistant request
      const assistantRequest: AssistantRequest = {
        generationType: type,
        conversationHistory: [],
        userMessage: initialPrompt,
        metadata: {
          projectContext: `Project: ${project?.name} - ${project?.description}`,
        },
      };

      // Get AI response
      const assistantResponse =
        await this.assistantService.processRequest(assistantRequest);

      // Get next message index
      const lastMessage =
        await this.prismaService.conversationMessage.findFirst({
          where: { generationId },
          orderBy: { messageIndex: 'desc' },
        });

      const nextIndex = (lastMessage?.messageIndex || 0) + 1;

      // Save assistant response
      await this.prismaService.conversationMessage.create({
        data: {
          generationId,
          role: MessageRole.ASSISTANT,
          content: assistantResponse.content,
          messageIndex: nextIndex,
          outputData: assistantResponse.outputData,
          status: MessageStatus.COMPLETED,
          processingTime: assistantResponse.processingTime,
        },
      });

      // Update generation with result
      await this.prismaService.generation.update({
        where: { id: generationId },
        data: {
          status: GenerationStatus.COMPLETED,
          result:
            assistantResponse.outputData?.code || assistantResponse.content,
          currentResult:
            assistantResponse.outputData?.code || assistantResponse.content,
          metadata: {
            ...assistantResponse.outputData?.metadata,
            model: assistantResponse.model,
            processingTime: assistantResponse.processingTime,
          },
        },
      });

      this.logger.log(`Initial generation completed for ${generationId}`);
    } catch (error) {
      this.logger.error('Failed to process initial generation', error);

      // Update generation status to FAILED
      await this.prismaService.generation.update({
        where: { id: generationId },
        data: { status: GenerationStatus.FAILED },
      });

      // Add error message to conversation
      const lastMessage =
        await this.prismaService.conversationMessage.findFirst({
          where: { generationId },
          orderBy: { messageIndex: 'desc' },
        });

      const nextIndex = (lastMessage?.messageIndex || 0) + 1;

      await this.prismaService.conversationMessage.create({
        data: {
          generationId,
          role: MessageRole.ASSISTANT,
          content:
            'I apologize, but I encountered an error while processing your request. Please try again.',
          messageIndex: nextIndex,
          status: MessageStatus.FAILED,
          errorMessage:
            error instanceof Error ? error.message : 'Unknown error',
        },
      });
    }
  }

  private async processConversationMessage(
    generationId: string,
    type: GenerationType,
    projectId: string,
  ) {
    try {
      // Get conversation history
      const messages = await this.prismaService.conversationMessage.findMany({
        where: { generationId },
        orderBy: { messageIndex: 'asc' },
        include: {
          createdBy: {
            select: { id: true, fullName: true },
          },
        },
      });

      // Convert to assistant message format
      const conversationHistory: AssistantMessage[] = messages
        .filter((msg) => msg.role !== MessageRole.SYSTEM)
        .map((msg) => ({
          role: msg.role,
          content: msg.content,
          inputData: msg.inputData,
          outputData: msg.outputData,
        }));

      // Get the latest user message
      const latestUserMessage = messages
        .filter((msg) => msg.role === MessageRole.USER)
        .pop();

      if (!latestUserMessage) {
        throw new Error('No user message found');
      }

      // Get project context
      const project = await this.prismaService.project.findUnique({
        where: { id: projectId },
        select: { name: true, description: true },
      });

      // Prepare assistant request
      const assistantRequest: AssistantRequest = {
        generationType: type,
        conversationHistory: conversationHistory.slice(0, -1), // Exclude the latest message
        userMessage: latestUserMessage.content,
        inputData: latestUserMessage.inputData,
        metadata: {
          projectContext: `Project: ${project?.name} - ${project?.description}`,
        },
      };

      // Get AI response
      const assistantResponse =
        await this.assistantService.processRequest(assistantRequest);

      // Get next message index
      const lastMessage =
        await this.prismaService.conversationMessage.findFirst({
          where: { generationId },
          orderBy: { messageIndex: 'desc' },
        });

      const nextIndex = (lastMessage?.messageIndex || 0) + 1;

      // Save assistant response
      await this.prismaService.conversationMessage.create({
        data: {
          generationId,
          role: MessageRole.ASSISTANT,
          content: assistantResponse.content,
          messageIndex: nextIndex,
          outputData: assistantResponse.outputData,
          status: MessageStatus.COMPLETED,
          processingTime: assistantResponse.processingTime,
        },
      });

      // Update generation with latest result
      await this.prismaService.generation.update({
        where: { id: generationId },
        data: {
          result:
            assistantResponse.outputData?.code || assistantResponse.content,
          currentResult:
            assistantResponse.outputData?.code || assistantResponse.content,
          metadata: {
            ...assistantResponse.outputData?.metadata,
            model: assistantResponse.model,
            lastProcessingTime: assistantResponse.processingTime,
          },
          updatedAt: new Date(),
        },
      });

      this.logger.log(`Conversation message processed for ${generationId}`);
    } catch (error) {
      this.logger.error('Failed to process conversation message', error);

      const lastMessage =
        await this.prismaService.conversationMessage.findFirst({
          where: { generationId },
          orderBy: { messageIndex: 'desc' },
        });

      const nextIndex = (lastMessage?.messageIndex || 0) + 1;

      await this.prismaService.conversationMessage.create({
        data: {
          generationId,
          role: MessageRole.ASSISTANT,
          content:
            'I apologize, but I encountered an error while processing your message. Please try again.',
          messageIndex: nextIndex,
          status: MessageStatus.FAILED,
          errorMessage:
            error instanceof Error ? error.message : 'Unknown error',
        },
      });
    }
  }

  private async validateGenerationAccess(
    generationId: string,
    userId: string,
    userRole: UserRole,
  ) {
    const generation = await this.prismaService.generation.findUnique({
      where: { id: generationId },
      include: {
        project: {
          select: {
            id: true,
            name: true,
            createdById: true,
          },
        },
      },
    });

    if (!generation) {
      throw new NotFoundException('Generation not found');
    }

    // Check access permissions
    if (userRole === UserRole.ADMIN) {
      return generation;
    }

    if (generation.project.createdById === userId) {
      return generation;
    }

    const membership = await this.prismaService.projectMember.findFirst({
      where: {
        projectId: generation.project.id,
        userId: userId,
      },
    });

    if (!membership) {
      throw new NotFoundException('Generation not found or access denied');
    }

    return generation;
  }

  async listGenerationsForProject(
    projectId: string,
    userId: string,
    userRole: UserRole,
  ) {
    try {
      const generations = await this.prismaService.generation.findMany({
        where: {
          projectId,
          ...(userRole !== UserRole.ADMIN && {
            project: {
              OR: [
                { createdById: userId },
                { members: { some: { userId: userId } } },
              ],
            },
          }),
        },
        include: {
          project: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      return {
        message: 'Generations listed successfully',
        data: generations,
        statusCode: 200,
      };
    } catch (error) {
      this.logger.error('Failed to list generations for project', error);
      throw new BadRequestException('Failed to list generations');
    }
  }

  async getGenerationById(
    generationId: string,
    userId: string,
    userRole: UserRole,
  ) {
    try {
      const generation = await this.validateGenerationAccess(
        generationId,
        userId,
        userRole,
      );

      return {
        message: 'Generation retrieved successfully',
        data: {
          id: generation.id,
          name: generation.name,
          type: generation.type,
          prompt: generation.prompt,
          initialPrompt: generation.initialPrompt,
          status: generation.status,
          result: generation.result,
          currentResult: generation.currentResult,
          metadata: generation.metadata,
          project: generation.project,
          createdAt: generation.createdAt,
          updatedAt: generation.updatedAt,
        },
        statusCode: 200,
      };
    } catch (error) {
      this.logger.error('Failed to get generation', error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Failed to get generation');
    }
  }

  streamGenerationEvents(generationId: string): Observable<MessageEvent> {
    return new Observable((observer) => {
      const pollGeneration = async () => {
        try {
          // Get generation without access validation (public endpoint)
          const generation = await this.prismaService.generation.findUnique({
            where: { id: generationId },
            include: {
              project: {
                select: {
                  id: true,
                  name: true,
                  createdById: true,
                },
              },
            },
          });

          if (!generation) {
            observer.error(new NotFoundException('Generation not found'));
            return;
          }

          // Send initial status
          observer.next({
            data: JSON.stringify({
              type: 'status',
              status: generation.status,
              generationId: generation.id,
              name: generation.name,
              timestamp: new Date().toISOString(),
            }),
          } as MessageEvent);

          if (generation.status === GenerationStatus.COMPLETED) {
            observer.next({
              data: JSON.stringify({
                type: 'completed',
                result: generation.currentResult,
                metadata: generation.metadata,
                generationId: generation.id,
                timestamp: new Date().toISOString(),
              }),
            } as MessageEvent);
            observer.complete();
            return;
          }

          if (generation.status === GenerationStatus.FAILED) {
            observer.next({
              data: JSON.stringify({
                type: 'failed',
                error: 'Generation failed',
                generationId: generation.id,
                timestamp: new Date().toISOString(),
              }),
            } as MessageEvent);
            observer.complete();
            return;
          }

          // Start polling for updates
          const pollInterval = setInterval(async () => {
            try {
              const updatedGeneration =
                await this.prismaService.generation.findUnique({
                  where: { id: generationId },
                  include: {
                    project: {
                      select: {
                        id: true,
                        name: true,
                        createdById: true,
                      },
                    },
                  },
                });

              if (!updatedGeneration) {
                clearInterval(pollInterval);
                observer.error(new NotFoundException('Generation not found'));
                return;
              }

              // Send progress update
              observer.next({
                data: JSON.stringify({
                  type: 'progress',
                  status: updatedGeneration.status,
                  generationId: updatedGeneration.id,
                  timestamp: new Date().toISOString(),
                }),
              } as MessageEvent);

              // Check if generation is completed
              if (updatedGeneration.status === GenerationStatus.COMPLETED) {
                clearInterval(pollInterval);
                observer.next({
                  data: JSON.stringify({
                    type: 'completed',
                    result: updatedGeneration.currentResult,
                    metadata: updatedGeneration.metadata,
                    generationId: updatedGeneration.id,
                    timestamp: new Date().toISOString(),
                  }),
                } as MessageEvent);
                observer.complete();
              } else if (updatedGeneration.status === GenerationStatus.FAILED) {
                clearInterval(pollInterval);
                observer.next({
                  data: JSON.stringify({
                    type: 'failed',
                    error: 'Generation failed',
                    generationId: updatedGeneration.id,
                    timestamp: new Date().toISOString(),
                  }),
                } as MessageEvent);
                observer.complete();
              }
            } catch (error) {
              clearInterval(pollInterval);
              this.logger.error('Error polling generation status', error);
              observer.error(error);
            }
          }, 1000); // Poll every second

          // Clean up on client disconnect
          return () => {
            clearInterval(pollInterval);
          };
        } catch (error) {
          this.logger.error('Error setting up generation stream', error);
          observer.error(error);
        }
      };

      void pollGeneration();
    });
  }
}
