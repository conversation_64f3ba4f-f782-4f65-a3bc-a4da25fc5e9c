import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  Query,
  Request,
  UseGuards,
  HttpStatus,
  Sse,
  MessageEvent,
  UnauthorizedException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { GenerationService } from './generation.service';
import { CreateConversationalGenerationDto } from './dto/create-conversational-generation.dto';
import { AddMessageDto } from './dto/add-message.dto';
import { GetConversationDto } from './dto/get-conversation.dto';
import { AuthGuard } from '../auth/guards/auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { AuthenticatedRequest } from '../auth/interfaces/authenticated-request.interface';
import { AuthTokenPayload } from '../token/interfaces/payload.interface';
import { Observable } from 'rxjs';

@ApiTags('generations')
@ApiBearerAuth()
@Controller('generations')
@UseGuards(AuthGuard, RolesGuard)
export class GenerationController {
  constructor(
    private readonly generationService: GenerationService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {}

  @Post('conversational')
  @ApiOperation({
    summary: 'Create a new conversational generation',
    description: 'Creates a new generation with conversational memory support',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Conversational generation created successfully',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Project not found or access denied',
  })
  async createConversationalGeneration(
    @Body() createDto: CreateConversationalGenerationDto,
    @Request() req: AuthenticatedRequest,
  ) {
    return this.generationService.createConversationalGeneration(
      createDto.projectId,
      createDto.name,
      createDto.type,
      createDto.initialPrompt,
      req.user.sub,
    );
  }

  @Post(':id/messages')
  @ApiOperation({
    summary: 'Add a message to conversation',
    description: 'Adds a new message to an existing conversational generation',
  })
  @ApiParam({
    name: 'id',
    description: 'Generation ID',
    format: 'uuid',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Message added to conversation successfully',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Generation not found or access denied',
  })
  async addMessageToConversation(
    @Param('id') generationId: string,
    @Body() addMessageDto: AddMessageDto,
    @Request() req: AuthenticatedRequest,
  ) {
    return this.generationService.addMessageToConversation(
      generationId,
      addMessageDto.content,
      req.user.sub,
      addMessageDto.inputData,
    );
  }

  @Get(':id/conversation')
  @ApiOperation({
    summary: 'Get conversation history',
    description: 'Retrieves the conversation history for a generation',
  })
  @ApiParam({
    name: 'id',
    description: 'Generation ID',
    format: 'uuid',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Conversation history retrieved successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Generation not found or access denied',
  })
  async getConversationHistory(
    @Param('id') generationId: string,
    @Query() query: GetConversationDto,
    @Request() req: AuthenticatedRequest,
  ) {
    return this.generationService.getConversationHistory(
      generationId,
      req.user.sub,
      req.user.role,
      query.page,
      query.limit,
    );
  }

  @Get(':id/result')
  @ApiOperation({
    summary: 'Get current generation result',
    description: 'Retrieves the current result of a generation',
  })
  @ApiParam({
    name: 'id',
    description: 'Generation ID',
    format: 'uuid',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Current result retrieved successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Generation not found or access denied',
  })
  async getCurrentResult(
    @Param('id') generationId: string,
    @Request() req: AuthenticatedRequest,
  ) {
    return this.generationService.getCurrentResult(
      generationId,
      req.user.sub,
      req.user.role,
    );
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get generation by ID',
    description: 'Retrieves a generation by its ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Generation ID',
    format: 'uuid',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Generation retrieved successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Generation not found or access denied',
  })
  async getGenerationById(
    @Param('id') generationId: string,
    @Request() req: AuthenticatedRequest,
  ) {
    return this.generationService.getGenerationById(
      generationId,
      req.user.sub,
      req.user.role,
    );
  }

  @Sse(':id/events')
  @ApiOperation({
    summary: 'Stream generation events',
    description:
      'Streams real-time events for a generation using Server-Sent Events',
  })
  @ApiParam({
    name: 'id',
    description: 'Generation ID',
    format: 'uuid',
  })
  @ApiQuery({
    name: 'token',
    description: 'JWT authentication token',
    required: true,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Streaming generation events',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Invalid or missing token',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Generation not found or access denied',
  })
  async streamGenerationEvents(
    @Param('id') generationId: string,
    @Query('token') token: string,
  ): Promise<Observable<MessageEvent>> {
    if (!token) {
      throw new UnauthorizedException('Token is required');
    }

    try {
      // Get the JWT secret
      const jwtSecret =
        this.configService.get<string>('JWT_SECRET') || 'default_secret';
      console.log('SSE: Using JWT secret:', jwtSecret.substring(0, 10) + '...');
      console.log('SSE: Token received:', token.substring(0, 50) + '...');

      // Verify the JWT token
      const payload = (await this.jwtService.verifyAsync(token, {
        secret: jwtSecret,
      })) as AuthTokenPayload;

      console.log('SSE: Token verified successfully for user:', payload.sub);

      return this.generationService.streamGenerationEvents(
        generationId,
        payload.sub,
        payload.role,
      );
    } catch (error) {
      console.error('SSE: Token verification failed:', error.message);
      throw new UnauthorizedException('Invalid token');
    }
  }

  @Get(':id/test-token')
  @ApiOperation({
    summary: 'Test token validation',
    description: 'Test endpoint to validate JWT token via query parameter',
  })
  async testTokenValidation(
    @Param('id') generationId: string,
    @Query('token') token: string,
  ) {
    if (!token) {
      throw new UnauthorizedException('Token is required');
    }

    try {
      const jwtSecret =
        this.configService.get<string>('JWT_SECRET') || 'default_secret';
      console.log(
        'TEST: Using JWT secret:',
        jwtSecret.substring(0, 10) + '...',
      );
      console.log('TEST: Token received:', token.substring(0, 50) + '...');

      const payload = (await this.jwtService.verifyAsync(token, {
        secret: jwtSecret,
      })) as AuthTokenPayload;

      console.log('TEST: Token verified successfully for user:', payload.sub);

      return {
        message: 'Token is valid',
        user: {
          id: payload.sub,
          email: payload.email,
          role: payload.role,
          status: payload.status,
        },
        generationId,
      };
    } catch (error) {
      console.error('TEST: Token verification failed:', error.message);
      throw new UnauthorizedException('Invalid token');
    }
  }
}

// Additional controller for project-based generation endpoints
@ApiTags('projects')
@ApiBearerAuth()
@Controller('projects')
@UseGuards(AuthGuard, RolesGuard)
export class ProjectGenerationController {
  constructor(private readonly generationService: GenerationService) {}

  @Post(':projectId/generations/conversational')
  @ApiOperation({
    summary: 'Create conversational generation for project',
    description:
      'Creates a new conversational generation within a specific project',
  })
  @ApiParam({
    name: 'projectId',
    description: 'Project ID',
    format: 'uuid',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Conversational generation created successfully',
  })
  async createProjectConversationalGeneration(
    @Param('projectId') projectId: string,
    @Body() createDto: Omit<CreateConversationalGenerationDto, 'projectId'>,
    @Request() req: AuthenticatedRequest,
  ) {
    return this.generationService.createConversationalGeneration(
      projectId,
      createDto.name,
      createDto.type,
      createDto.initialPrompt,
      req.user.sub,
    );
  }
}
