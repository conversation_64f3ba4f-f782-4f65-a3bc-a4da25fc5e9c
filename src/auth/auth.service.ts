import {
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  UnauthorizedException,
  ForbiddenException,
} from '@nestjs/common';
import { RegisterDto } from './dto/register-user.dto';
import { UsersService } from 'src/users/users.service';
import { SignInDto } from './dto/signin-user.dto';
import * as bcrypt from 'bcrypt';
import { TokenService } from 'src/token/token.service';
import { AuthTokenPayload } from 'src/token/interfaces/payload.interface';
import { UserStatus } from 'generated/prisma';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);
  constructor(
    private readonly usersService: UsersService,
    private readonly tokenService: TokenService,
  ) {}

  async signInUser(signInDto: SignInDto) {
    try {
      const user = await this.validateUser(signInDto);
      if (!user) {
        throw new UnauthorizedException('Invalid credentials');
      }

      // Check user status - only ACTIVE users can sign in
      if (user.status !== UserStatus.ACTIVE) {
        if (user.status === UserStatus.INACTIVE) {
          throw new ForbiddenException(
            'Your account is pending approval. Please wait for an administrator to activate your account.',
          );
        } else if (user.status === UserStatus.REJECTED) {
          throw new ForbiddenException(
            'Your account has been rejected. Please contact support for assistance.',
          );
        } else {
          throw new ForbiddenException(
            'Your account is not active. Please contact support.',
          );
        }
      }

      const payload: AuthTokenPayload = {
        email: user.email,
        sub: user.id,
        status: user.status,
        role: user.role,
        type: 'auth',
      };

      const accessToken = await this.tokenService.generateToken(payload);
      if (!accessToken) {
        throw new InternalServerErrorException(
          'Failed to generate access token',
        );
      }

      const refreshToken = await this.tokenService.generateToken(payload, {
        expiresIn: '7d',
      });
      if (!refreshToken) {
        throw new InternalServerErrorException(
          'Failed to generate refresh token',
        );
      }

      await this.tokenService.saveRefreshToken({
        userId: user.id,
        refreshToken: refreshToken.value,
      });

      return {
        message: 'User signed in successfully',
        data: {
          id: user.id,
          fullName: user.fullName,
          email: user.email,
          role: user.role,
          status: user.status,
          accessToken: accessToken.value,
          refreshToken: refreshToken.value,
        },
        statusCode: HttpStatus.OK,
      };
    } catch (error) {
      this.logger.error(error);
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof ForbiddenException ||
        error instanceof InternalServerErrorException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to sign in user');
    }
  }

  async registerUser(registerDto: RegisterDto) {
    try {
      const userResponse = await this.usersService.createUser({
        ...registerDto,
      });

      return {
        message: 'User registered successfully',
        data: userResponse.data,
        statusCode: HttpStatus.CREATED,
      };
    } catch (error) {
      this.logger.error(error);
      throw error; // Re-throw the error from users service
    }
  }

  async logoutUser(userId: string) {
    try {
      await this.tokenService.deleteRefreshToken(userId);
      return {
        message: 'User logged out successfully',
        statusCode: HttpStatus.OK,
      };
    } catch (error) {
      this.logger.error(error);
      if (error instanceof InternalServerErrorException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to log out user');
    }
  }

  async validateUser(signInDto: SignInDto) {
    const { email, password } = signInDto;
    const user = await this.usersService.findOne(email);
    if (!user) {
      throw new NotFoundException('User not found');
    }
    const isValidPassword = await bcrypt.compare(password, user?.passwordHash);

    return isValidPassword ? user : null;
  }
}
